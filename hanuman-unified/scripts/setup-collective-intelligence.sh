#!/bin/bash

# 🧠 JOUR 6: Intelligence Collective - Setup Script
# Roadmap Excellence 10/10 - Configuration de l'intelligence collective

set -e

echo "🧠 JOUR 6: INTELLIGENCE COLLECTIVE - SETUP"
echo "==========================================="
echo "🤖 Configuration de l'intelligence collective des agents"
echo "🎯 Objectif: Communication neuronale + Auto-learning + Consensus"
echo ""

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Fonction de logging
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_collective() {
    echo -e "${PURPLE}[COLLECTIVE]${NC} $1"
}

log_neural() {
    echo -e "${CYAN}[NEURAL]${NC} $1"
}

# Vérification des prérequis
check_prerequisites() {
    log_info "Vérification des prérequis intelligence collective..."
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js n'est pas installé"
        exit 1
    fi
    
    # Vérifier npm
    if ! command -v npm &> /dev/null; then
        log_error "npm n'est pas installé"
        exit 1
    fi
    
    # Vérifier la structure Hanuman
    if [ ! -d "brain" ] || [ ! -d "agents" ]; then
        log_error "Structure Hanuman non trouvée. Exécuter depuis hanuman-unified/"
        exit 1
    fi
    
    # Vérifier les agents existants
    local required_agents=("frontend" "backend" "devops" "qa" "security")
    for agent in "${required_agents[@]}"; do
        if [ ! -d "agents/$agent" ]; then
            log_warning "Agent $agent non trouvé - sera créé"
        fi
    done
    
    log_success "Prérequis vérifiés"
}

# Installation des dépendances intelligence collective
install_collective_dependencies() {
    log_info "Installation des dépendances intelligence collective..."
    
    # Dépendances communication neuronale
    npm install --save ws socket.io-client eventemitter3
    
    # Dépendances auto-learning
    npm install --save ml-matrix tensorflow @tensorflow/tfjs-node
    
    # Dépendances consensus et coordination
    npm install --save raft-consensus paxos-consensus
    
    # Dépendances monitoring intelligence
    npm install --save prometheus-client prom-client
    
    # Dépendances développement
    npm install --save-dev @types/ws @types/tensorflow
    
    log_success "Dépendances intelligence collective installées"
}

# Configuration des répertoires
setup_directories() {
    log_info "Configuration des répertoires intelligence collective..."
    
    # Créer les répertoires nécessaires
    mkdir -p collective-intelligence/{communication,learning,decision,behavior,monitoring}
    mkdir -p collective-intelligence/communication/{neural,protocols,shared-memory}
    mkdir -p collective-intelligence/learning/{knowledge-sharing,pattern-recognition,model-fusion}
    mkdir -p collective-intelligence/decision/{consensus,negotiation,allocation,resolution}
    mkdir -p collective-intelligence/behavior/{emergent,detection,control}
    mkdir -p collective-intelligence/monitoring/{metrics,dashboards,alerts}
    
    log_success "Répertoires intelligence collective configurés"
}

# Configuration des variables d'environnement
setup_environment() {
    log_info "Configuration des variables d'environnement..."
    
    # Créer le fichier .env.collective
    cat > .env.collective << EOF
# 🧠 JOUR 6: Intelligence Collective
# Configuration de l'intelligence collective des agents

# Neural Communication
NEURAL_NETWORK_HOST=localhost
NEURAL_NETWORK_PORT=8080
SYNAPTIC_PROTOCOL_VERSION=1.0
SHARED_MEMORY_SIZE=1024
COMMUNICATION_TIMEOUT=5000

# Auto-Learning
LEARNING_RATE=0.001
KNOWLEDGE_SHARING_INTERVAL=30000
PATTERN_RECOGNITION_THRESHOLD=0.85
MODEL_FUSION_STRATEGY=weighted_average
CONTINUOUS_LEARNING=true

# Collective Decision Making
CONSENSUS_ALGORITHM=raft
CONSENSUS_TIMEOUT=10000
PRIORITY_NEGOTIATION=true
RESOURCE_ALLOCATION_STRATEGY=fair_share
CONFLICT_RESOLUTION=democratic

# Emergent Behavior
BEHAVIOR_DETECTION=true
BEHAVIOR_ANALYSIS_INTERVAL=60000
EMERGENT_THRESHOLD=0.9
BEHAVIOR_CONTROL=adaptive

# Monitoring
COLLECTIVE_METRICS_PORT=9091
INTELLIGENCE_DASHBOARD_PORT=3002
METRICS_COLLECTION_INTERVAL=5000
ALERT_THRESHOLD_INTELLIGENCE=0.8

# Agent Configuration
MAX_AGENTS=10
AGENT_HEARTBEAT_INTERVAL=10000
AGENT_TIMEOUT=30000
LOAD_BALANCING=true
EOF

    log_success "Variables d'environnement configurées"
}

# Configuration initiale des services
setup_services() {
    log_info "Configuration initiale des services intelligence collective..."
    
    # Créer le service de coordination central
    cat > collective-intelligence/CollectiveIntelligenceCoordinator.ts << 'EOF'
import { EventEmitter } from 'eventemitter3';

export interface Agent {
    id: string;
    type: string;
    status: 'active' | 'inactive' | 'learning' | 'deciding';
    capabilities: string[];
    knowledge: Map<string, any>;
    lastHeartbeat: Date;
}

export interface CollectiveMetrics {
    totalAgents: number;
    activeAgents: number;
    communicationLatency: number;
    learningEfficiency: number;
    consensusTime: number;
    emergentBehaviors: number;
}

export class CollectiveIntelligenceCoordinator extends EventEmitter {
    private agents: Map<string, Agent> = new Map();
    private metrics: CollectiveMetrics;
    private isActive: boolean = false;

    constructor() {
        super();
        this.initializeMetrics();
        this.startHeartbeatMonitoring();
    }

    private initializeMetrics(): void {
        this.metrics = {
            totalAgents: 0,
            activeAgents: 0,
            communicationLatency: 0,
            learningEfficiency: 0,
            consensusTime: 0,
            emergentBehaviors: 0
        };
    }

    // Enregistrer un agent dans le collectif
    registerAgent(agent: Agent): boolean {
        try {
            this.agents.set(agent.id, {
                ...agent,
                lastHeartbeat: new Date()
            });
            
            this.metrics.totalAgents = this.agents.size;
            this.updateActiveAgents();
            
            console.log(`🤖 Agent ${agent.id} (${agent.type}) enregistré dans le collectif`);
            this.emit('agentRegistered', agent);
            
            return true;
        } catch (error) {
            console.error('Erreur enregistrement agent:', error);
            return false;
        }
    }

    // Désenregistrer un agent
    unregisterAgent(agentId: string): boolean {
        try {
            const agent = this.agents.get(agentId);
            if (agent) {
                this.agents.delete(agentId);
                this.metrics.totalAgents = this.agents.size;
                this.updateActiveAgents();
                
                console.log(`🤖 Agent ${agentId} désenregistré du collectif`);
                this.emit('agentUnregistered', agent);
                
                return true;
            }
            return false;
        } catch (error) {
            console.error('Erreur désenregistrement agent:', error);
            return false;
        }
    }

    // Mettre à jour le heartbeat d'un agent
    updateAgentHeartbeat(agentId: string): boolean {
        const agent = this.agents.get(agentId);
        if (agent) {
            agent.lastHeartbeat = new Date();
            this.updateActiveAgents();
            return true;
        }
        return false;
    }

    // Obtenir tous les agents actifs
    getActiveAgents(): Agent[] {
        const now = new Date();
        const timeout = parseInt(process.env.AGENT_TIMEOUT || '30000');
        
        return Array.from(this.agents.values()).filter(agent => {
            const timeSinceHeartbeat = now.getTime() - agent.lastHeartbeat.getTime();
            return timeSinceHeartbeat < timeout;
        });
    }

    // Obtenir les agents par type
    getAgentsByType(type: string): Agent[] {
        return this.getActiveAgents().filter(agent => agent.type === type);
    }

    // Obtenir les agents par capacité
    getAgentsByCapability(capability: string): Agent[] {
        return this.getActiveAgents().filter(agent => 
            agent.capabilities.includes(capability)
        );
    }

    // Diffuser un message à tous les agents
    broadcastToAgents(message: any, excludeAgent?: string): void {
        const activeAgents = this.getActiveAgents();
        
        activeAgents.forEach(agent => {
            if (agent.id !== excludeAgent) {
                this.sendMessageToAgent(agent.id, message);
            }
        });
        
        console.log(`📡 Message diffusé à ${activeAgents.length} agents`);
    }

    // Envoyer un message à un agent spécifique
    sendMessageToAgent(agentId: string, message: any): boolean {
        const agent = this.agents.get(agentId);
        if (agent) {
            // Simuler l'envoi de message
            this.emit('messageToAgent', { agentId, message });
            return true;
        }
        return false;
    }

    // Démarrer l'intelligence collective
    start(): void {
        if (!this.isActive) {
            this.isActive = true;
            console.log('🧠 Intelligence Collective démarrée');
            this.emit('collectiveStarted');
        }
    }

    // Arrêter l'intelligence collective
    stop(): void {
        if (this.isActive) {
            this.isActive = false;
            console.log('🧠 Intelligence Collective arrêtée');
            this.emit('collectiveStopped');
        }
    }

    // Obtenir les métriques du collectif
    getMetrics(): CollectiveMetrics {
        return { ...this.metrics };
    }

    // Monitoring du heartbeat
    private startHeartbeatMonitoring(): void {
        const interval = parseInt(process.env.AGENT_HEARTBEAT_INTERVAL || '10000');
        
        setInterval(() => {
            this.updateActiveAgents();
            this.checkAgentHealth();
        }, interval);
    }

    private updateActiveAgents(): void {
        this.metrics.activeAgents = this.getActiveAgents().length;
    }

    private checkAgentHealth(): void {
        const now = new Date();
        const timeout = parseInt(process.env.AGENT_TIMEOUT || '30000');
        
        this.agents.forEach((agent, agentId) => {
            const timeSinceHeartbeat = now.getTime() - agent.lastHeartbeat.getTime();
            
            if (timeSinceHeartbeat > timeout && agent.status === 'active') {
                agent.status = 'inactive';
                console.warn(`⚠️ Agent ${agentId} inactif (pas de heartbeat depuis ${timeSinceHeartbeat}ms)`);
                this.emit('agentInactive', agent);
            }
        });
    }

    // Vérifier la santé du collectif
    healthCheck(): { healthy: boolean; details: any } {
        const activeAgents = this.getActiveAgents();
        const healthyThreshold = 0.8; // 80% des agents doivent être actifs
        
        const healthRatio = this.metrics.totalAgents > 0 ? 
            activeAgents.length / this.metrics.totalAgents : 0;
        
        const healthy = healthRatio >= healthyThreshold;
        
        return {
            healthy,
            details: {
                totalAgents: this.metrics.totalAgents,
                activeAgents: activeAgents.length,
                healthRatio: healthRatio,
                isActive: this.isActive,
                metrics: this.metrics
            }
        };
    }
}

// Instance globale du coordinateur
export const collectiveCoordinator = new CollectiveIntelligenceCoordinator();

// Auto-initialisation
if (typeof process !== 'undefined' && process.env.NODE_ENV !== 'test') {
    console.log('🧠 Initialisation du Coordinateur Intelligence Collective...');
    collectiveCoordinator.start();
}
EOF

    log_success "Services intelligence collective configurés"
}

# Génération des fichiers de configuration
generate_config_files() {
    log_info "Génération des fichiers de configuration..."
    
    # Configuration communication neuronale
    cat > collective-intelligence/communication/neural-config.json << EOF
{
  "neuralNetwork": {
    "host": "localhost",
    "port": 8080,
    "protocol": "ws",
    "maxConnections": 100,
    "heartbeatInterval": 10000,
    "reconnectAttempts": 5,
    "reconnectDelay": 2000
  },
  "synapticProtocol": {
    "version": "1.0",
    "messageFormat": "json",
    "compression": true,
    "encryption": false,
    "maxMessageSize": 1048576
  },
  "sharedMemory": {
    "size": 1024,
    "segments": ["knowledge", "decisions", "behaviors", "metrics"],
    "syncInterval": 5000,
    "persistToDisk": true
  }
}
EOF

    # Configuration auto-learning
    cat > collective-intelligence/learning/learning-config.json << EOF
{
  "autoLearning": {
    "enabled": true,
    "learningRate": 0.001,
    "batchSize": 32,
    "epochs": 100,
    "validationSplit": 0.2
  },
  "knowledgeSharing": {
    "interval": 30000,
    "strategy": "broadcast",
    "compression": true,
    "maxKnowledgeSize": 10485760
  },
  "patternRecognition": {
    "threshold": 0.85,
    "algorithms": ["neural", "clustering", "classification"],
    "updateInterval": 60000
  },
  "modelFusion": {
    "strategy": "weighted_average",
    "weights": "performance_based",
    "fusionInterval": 300000
  }
}
EOF

    # Configuration decision making
    cat > collective-intelligence/decision/decision-config.json << EOF
{
  "consensus": {
    "algorithm": "raft",
    "timeout": 10000,
    "retries": 3,
    "quorum": "majority"
  },
  "negotiation": {
    "enabled": true,
    "maxRounds": 10,
    "timeoutPerRound": 5000,
    "strategy": "cooperative"
  },
  "resourceAllocation": {
    "strategy": "fair_share",
    "rebalanceInterval": 60000,
    "maxResourcePerAgent": 0.5
  },
  "conflictResolution": {
    "method": "democratic",
    "votingTimeout": 15000,
    "escalationLevels": ["peer", "coordinator", "human"]
  }
}
EOF

    log_success "Fichiers de configuration générés"
}

# Fonction principale
main() {
    echo "🧠 DÉMARRAGE SETUP INTELLIGENCE COLLECTIVE"
    echo "==========================================="
    
    check_prerequisites
    install_collective_dependencies
    setup_directories
    setup_environment
    setup_services
    generate_config_files
    
    echo ""
    echo "✅ SETUP INTELLIGENCE COLLECTIVE TERMINÉ"
    echo "========================================"
    echo "📊 Prochaines étapes:"
    echo "  1. ./scripts/configure-neural-communication.sh"
    echo "  2. ./scripts/implement-auto-learning.sh"
    echo "  3. ./scripts/setup-collective-decision.sh"
    echo "  4. ./scripts/test-emergent-behavior.sh"
    echo ""
    echo "🎯 Objectifs Jour 6:"
    echo "  • Communication neuronale active"
    echo "  • Auto-learning collectif opérationnel"
    echo "  • Decision making par consensus"
    echo "  • Emergent behavior détecté"
    echo ""
    
    log_success "Intelligence Collective Setup - SUCCÈS COMPLET! 🧠"
}

# Exécution du script principal
main "$@"
