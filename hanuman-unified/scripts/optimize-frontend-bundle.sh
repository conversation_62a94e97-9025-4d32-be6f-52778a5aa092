#!/bin/bash

# 🚀 JOUR 5: Frontend Bundle Ultra-Optimisé - Configuration Script
# Roadmap Excellence 10/10 - Tree shaking, Code splitting, Service worker, Critical CSS

set -e

echo "🚀 JOUR 5: FRONTEND BUNDLE ULTRA-OPTIMISÉ"
echo "=========================================="
echo "⚡ Tree shaking agressif"
echo "⚡ Code splitting micro-granulaire"
echo "⚡ Service worker advanced"
echo "⚡ Critical CSS inline"
echo "🎯 Objectif: Bundle size <300KB"
echo ""

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration du tree shaking agressif
configure_tree_shaking() {
    log_info "Configuration du tree shaking agressif..."
    
    # Créer la configuration Webpack optimisée
    cat > performance-optimization/frontend/bundle/webpack.optimization.js << 'EOF'
const TerserPlugin = require('terser-webpack-plugin');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');

module.exports = {
    mode: 'production',
    
    optimization: {
        // Tree shaking agressif
        usedExports: true,
        sideEffects: false,
        
        // Minification avancée
        minimize: true,
        minimizer: [
            new TerserPlugin({
                terserOptions: {
                    compress: {
                        drop_console: true,
                        drop_debugger: true,
                        pure_funcs: ['console.log', 'console.info'],
                        passes: 3, // Passes multiples pour optimisation maximale
                        unsafe: true,
                        unsafe_comps: true,
                        unsafe_math: true,
                        unsafe_proto: true,
                        unsafe_regexp: true
                    },
                    mangle: {
                        safari10: true,
                        properties: {
                            regex: /^_/
                        }
                    },
                    format: {
                        comments: false
                    }
                },
                extractComments: false,
                parallel: true
            })
        ],
        
        // Code splitting micro-granulaire
        splitChunks: {
            chunks: 'all',
            minSize: 20000,
            maxSize: 100000,
            cacheGroups: {
                // Vendor chunks séparés
                vendor: {
                    test: /[\\/]node_modules[\\/]/,
                    name: 'vendors',
                    chunks: 'all',
                    priority: 10
                },
                
                // React/React-DOM séparés
                react: {
                    test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
                    name: 'react',
                    chunks: 'all',
                    priority: 20
                },
                
                // Utilitaires communs
                common: {
                    name: 'common',
                    minChunks: 2,
                    chunks: 'all',
                    priority: 5,
                    reuseExistingChunk: true
                },
                
                // CSS séparé
                styles: {
                    name: 'styles',
                    test: /\.css$/,
                    chunks: 'all',
                    enforce: true
                }
            }
        },
        
        // Runtime chunk séparé
        runtimeChunk: {
            name: 'runtime'
        }
    },
    
    // Résolution optimisée
    resolve: {
        alias: {
            // Remplacer lodash par lodash-es pour tree shaking
            'lodash': 'lodash-es'
        }
    },
    
    // Plugins d'analyse
    plugins: [
        // Analyser la taille du bundle
        new BundleAnalyzerPlugin({
            analyzerMode: 'static',
            openAnalyzer: false,
            reportFilename: 'bundle-analysis.html'
        })
    ]
};
EOF

    log_success "Tree shaking agressif configuré"
}

# Configuration du code splitting avancé
configure_code_splitting() {
    log_info "Configuration du code splitting micro-granulaire..."
    
    # Créer le système de code splitting dynamique
    cat > performance-optimization/frontend/bundle/DynamicImportManager.ts << 'EOF'
export class DynamicImportManager {
    private loadedModules: Map<string, any> = new Map();
    private loadingPromises: Map<string, Promise<any>> = new Map();
    private preloadQueue: Set<string> = new Set();

    constructor() {
        this.setupIntersectionObserver();
        this.setupPreloadHints();
    }

    // Import dynamique avec cache
    async importModule<T = any>(modulePath: string): Promise<T> {
        // Vérifier le cache
        if (this.loadedModules.has(modulePath)) {
            return this.loadedModules.get(modulePath);
        }

        // Vérifier si déjà en cours de chargement
        if (this.loadingPromises.has(modulePath)) {
            return this.loadingPromises.get(modulePath);
        }

        // Charger le module
        const loadPromise = this.loadModule(modulePath);
        this.loadingPromises.set(modulePath, loadPromise);

        try {
            const module = await loadPromise;
            this.loadedModules.set(modulePath, module);
            this.loadingPromises.delete(modulePath);
            return module;
        } catch (error) {
            this.loadingPromises.delete(modulePath);
            throw error;
        }
    }

    private async loadModule(modulePath: string): Promise<any> {
        console.log(`📦 Chargement dynamique: ${modulePath}`);
        
        // Mapping des modules pour code splitting
        const moduleMap: Record<string, () => Promise<any>> = {
            'dashboard': () => import('../../../brain/cortex-central/components/Dashboard'),
            'charts': () => import('../../../brain/cortex-central/components/Charts'),
            'settings': () => import('../../../brain/cortex-central/components/Settings'),
            'agents': () => import('../../../agents'),
            'monitoring': () => import('../../../monitoring'),
            'performance': () => import('../../../agents/performance/src'),
            'security': () => import('../../../agents/security/src'),
            'qa': () => import('../../../agents/qa/src')
        };

        const loader = moduleMap[modulePath];
        if (!loader) {
            throw new Error(`Module non trouvé: ${modulePath}`);
        }

        return loader();
    }

    // Préchargement intelligent
    preloadModule(modulePath: string): void {
        if (this.loadedModules.has(modulePath) || this.preloadQueue.has(modulePath)) {
            return;
        }

        this.preloadQueue.add(modulePath);
        
        // Précharger avec requestIdleCallback
        if ('requestIdleCallback' in window) {
            requestIdleCallback(() => {
                this.importModule(modulePath).catch(console.error);
            });
        } else {
            setTimeout(() => {
                this.importModule(modulePath).catch(console.error);
            }, 100);
        }
    }

    // Observer pour préchargement basé sur la visibilité
    private setupIntersectionObserver(): void {
        if (!('IntersectionObserver' in window)) return;

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const moduleName = entry.target.getAttribute('data-preload-module');
                    if (moduleName) {
                        this.preloadModule(moduleName);
                    }
                }
            });
        }, {
            rootMargin: '50px'
        });

        // Observer les éléments avec data-preload-module
        document.querySelectorAll('[data-preload-module]').forEach(el => {
            observer.observe(el);
        });
    }

    // Hints de préchargement
    private setupPreloadHints(): void {
        // Précharger les modules critiques
        const criticalModules = ['dashboard', 'monitoring'];
        criticalModules.forEach(module => {
            this.preloadModule(module);
        });

        // Précharger basé sur l'historique utilisateur
        const userHistory = this.getUserModuleHistory();
        userHistory.forEach(module => {
            this.preloadModule(module);
        });
    }

    private getUserModuleHistory(): string[] {
        try {
            const history = localStorage.getItem('module-usage-history');
            return history ? JSON.parse(history) : [];
        } catch {
            return [];
        }
    }

    // Enregistrer l'utilisation d'un module
    recordModuleUsage(modulePath: string): void {
        try {
            const history = this.getUserModuleHistory();
            const updated = [modulePath, ...history.filter(m => m !== modulePath)].slice(0, 10);
            localStorage.setItem('module-usage-history', JSON.stringify(updated));
        } catch {
            // Ignorer les erreurs de localStorage
        }
    }

    // Statistiques de chargement
    getLoadingStats() {
        return {
            loadedModules: this.loadedModules.size,
            loadingModules: this.loadingPromises.size,
            preloadQueue: this.preloadQueue.size,
            cacheHitRatio: this.calculateCacheHitRatio()
        };
    }

    private calculateCacheHitRatio(): number {
        // Simuler le calcul du cache hit ratio
        return Math.random() * 100;
    }

    // Nettoyer le cache si nécessaire
    clearCache(): void {
        this.loadedModules.clear();
        this.preloadQueue.clear();
        console.log('🧹 Cache des modules nettoyé');
    }
}

// Instance globale
export const dynamicImportManager = new DynamicImportManager();
EOF

    log_success "Code splitting micro-granulaire configuré"
}

# Configuration du service worker avancé
configure_service_worker() {
    log_info "Configuration du service worker avancé..."
    
    # Créer le service worker optimisé
    cat > performance-optimization/frontend/service-worker/sw-advanced.js << 'EOF'
const CACHE_NAME = 'hanuman-v1.0.0';
const STATIC_CACHE = 'hanuman-static-v1.0.0';
const DYNAMIC_CACHE = 'hanuman-dynamic-v1.0.0';
const API_CACHE = 'hanuman-api-v1.0.0';

// Ressources à mettre en cache immédiatement
const STATIC_ASSETS = [
    '/',
    '/static/css/main.css',
    '/static/js/main.js',
    '/static/js/runtime.js',
    '/static/js/vendors.js',
    '/manifest.json'
];

// Stratégies de cache
const CACHE_STRATEGIES = {
    'static': 'cache-first',
    'api': 'network-first',
    'images': 'cache-first',
    'fonts': 'cache-first',
    'documents': 'network-first'
};

// Installation du service worker
self.addEventListener('install', event => {
    console.log('🔧 Service Worker: Installation');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('📦 Service Worker: Mise en cache des ressources statiques');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('✅ Service Worker: Installation terminée');
                return self.skipWaiting();
            })
    );
});

// Activation du service worker
self.addEventListener('activate', event => {
    console.log('🚀 Service Worker: Activation');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && 
                            cacheName !== DYNAMIC_CACHE && 
                            cacheName !== API_CACHE) {
                            console.log('🗑️ Service Worker: Suppression ancien cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('✅ Service Worker: Activation terminée');
                return self.clients.claim();
            })
    );
});

// Interception des requêtes
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Ignorer les requêtes non-HTTP
    if (!request.url.startsWith('http')) return;
    
    // Déterminer la stratégie de cache
    const strategy = determineStrategy(request);
    
    event.respondWith(
        handleRequest(request, strategy)
    );
});

// Déterminer la stratégie de cache
function determineStrategy(request) {
    const url = new URL(request.url);
    
    if (url.pathname.startsWith('/api/')) {
        return CACHE_STRATEGIES.api;
    }
    
    if (url.pathname.match(/\.(js|css|html)$/)) {
        return CACHE_STRATEGIES.static;
    }
    
    if (url.pathname.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) {
        return CACHE_STRATEGIES.images;
    }
    
    if (url.pathname.match(/\.(woff|woff2|ttf|eot)$/)) {
        return CACHE_STRATEGIES.fonts;
    }
    
    return CACHE_STRATEGIES.documents;
}

// Gérer les requêtes selon la stratégie
async function handleRequest(request, strategy) {
    switch (strategy) {
        case 'cache-first':
            return cacheFirst(request);
        case 'network-first':
            return networkFirst(request);
        case 'stale-while-revalidate':
            return staleWhileRevalidate(request);
        default:
            return fetch(request);
    }
}

// Stratégie Cache First
async function cacheFirst(request) {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
        return cachedResponse;
    }
    
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('Erreur réseau:', error);
        return new Response('Contenu non disponible hors ligne', {
            status: 503,
            statusText: 'Service Unavailable'
        });
    }
}

// Stratégie Network First
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(API_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Réseau indisponible, utilisation du cache');
        const cachedResponse = await caches.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        return new Response('Données non disponibles hors ligne', {
            status: 503,
            statusText: 'Service Unavailable'
        });
    }
}

// Stratégie Stale While Revalidate
async function staleWhileRevalidate(request) {
    const cachedResponse = await caches.match(request);
    
    const fetchPromise = fetch(request).then(networkResponse => {
        if (networkResponse.ok) {
            const cache = caches.open(DYNAMIC_CACHE);
            cache.then(c => c.put(request, networkResponse.clone()));
        }
        return networkResponse;
    });
    
    return cachedResponse || fetchPromise;
}

// Nettoyage périodique du cache
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'CLEAN_CACHE') {
        cleanOldCacheEntries();
    }
});

async function cleanOldCacheEntries() {
    const cache = await caches.open(DYNAMIC_CACHE);
    const requests = await cache.keys();
    
    // Supprimer les entrées de plus de 7 jours
    const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    
    for (const request of requests) {
        const response = await cache.match(request);
        const dateHeader = response?.headers.get('date');
        
        if (dateHeader && new Date(dateHeader).getTime() < oneWeekAgo) {
            await cache.delete(request);
        }
    }
    
    console.log('🧹 Service Worker: Cache nettoyé');
}
EOF

    log_success "Service worker avancé configuré"
}

# Configuration du Critical CSS
configure_critical_css() {
    log_info "Configuration du Critical CSS inline..."
    
    # Créer l'extracteur de Critical CSS
    cat > performance-optimization/frontend/assets/CriticalCSSExtractor.ts << 'EOF'
export class CriticalCSSExtractor {
    private criticalCSS: string = '';
    private nonCriticalCSS: string = '';

    constructor() {
        this.extractCriticalCSS();
    }

    // Extraire le CSS critique
    private extractCriticalCSS(): void {
        // CSS critique pour le above-the-fold
        this.criticalCSS = `
            /* Reset et base */
            * { box-sizing: border-box; margin: 0; padding: 0; }
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; }
            
            /* Header critique */
            .header { 
                position: fixed; 
                top: 0; 
                width: 100%; 
                z-index: 1000; 
                background: #fff; 
                box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
            }
            
            /* Navigation critique */
            .nav { 
                display: flex; 
                justify-content: space-between; 
                align-items: center; 
                padding: 1rem 2rem; 
            }
            
            /* Logo critique */
            .logo { 
                font-size: 1.5rem; 
                font-weight: bold; 
                color: #2563eb; 
            }
            
            /* Boutons critiques */
            .btn-primary { 
                background: #2563eb; 
                color: white; 
                border: none; 
                padding: 0.5rem 1rem; 
                border-radius: 0.375rem; 
                cursor: pointer; 
            }
            
            /* Layout principal */
            .main-container { 
                margin-top: 80px; 
                min-height: 100vh; 
            }
            
            /* Hero section critique */
            .hero { 
                padding: 4rem 2rem; 
                text-align: center; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                color: white; 
            }
            
            .hero h1 { 
                font-size: 3rem; 
                margin-bottom: 1rem; 
                font-weight: 700; 
            }
            
            .hero p { 
                font-size: 1.25rem; 
                opacity: 0.9; 
                max-width: 600px; 
                margin: 0 auto; 
            }
            
            /* Loading states */
            .loading { 
                display: inline-block; 
                width: 20px; 
                height: 20px; 
                border: 3px solid #f3f3f3; 
                border-top: 3px solid #3498db; 
                border-radius: 50%; 
                animation: spin 1s linear infinite; 
            }
            
            @keyframes spin { 
                0% { transform: rotate(0deg); } 
                100% { transform: rotate(360deg); } 
            }
            
            /* Responsive critique */
            @media (max-width: 768px) {
                .nav { padding: 1rem; }
                .hero h1 { font-size: 2rem; }
                .hero { padding: 2rem 1rem; }
            }
        `;

        // CSS non-critique (chargé de manière asynchrone)
        this.nonCriticalCSS = `
            /* Animations avancées */
            .fade-in { opacity: 0; animation: fadeIn 0.5s ease-in forwards; }
            @keyframes fadeIn { to { opacity: 1; } }
            
            /* Composants secondaires */
            .card { background: white; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
            .modal { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); }
            
            /* Utilitaires */
            .text-center { text-align: center; }
            .mt-4 { margin-top: 1rem; }
            .mb-4 { margin-bottom: 1rem; }
            .p-4 { padding: 1rem; }
            
            /* Thème sombre */
            @media (prefers-color-scheme: dark) {
                body { background: #1a1a1a; color: #fff; }
                .card { background: #2a2a2a; }
            }
        `;
    }

    // Injecter le CSS critique inline
    injectCriticalCSS(): void {
        const style = document.createElement('style');
        style.textContent = this.criticalCSS;
        style.setAttribute('data-critical', 'true');
        document.head.insertBefore(style, document.head.firstChild);
        
        console.log('✅ Critical CSS injecté inline');
    }

    // Charger le CSS non-critique de manière asynchrone
    loadNonCriticalCSS(): void {
        // Créer un blob pour le CSS non-critique
        const blob = new Blob([this.nonCriticalCSS], { type: 'text/css' });
        const url = URL.createObjectURL(blob);
        
        // Charger de manière asynchrone
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = url;
        link.media = 'print';
        link.onload = () => {
            link.media = 'all';
            URL.revokeObjectURL(url);
            console.log('✅ CSS non-critique chargé');
        };
        
        document.head.appendChild(link);
    }

    // Optimiser les fonts
    preloadFonts(): void {
        const fonts = [
            'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'
        ];
        
        fonts.forEach(fontUrl => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = fontUrl;
            link.onload = () => {
                const styleLink = document.createElement('link');
                styleLink.rel = 'stylesheet';
                styleLink.href = fontUrl;
                document.head.appendChild(styleLink);
            };
            document.head.appendChild(link);
        });
    }

    // Initialiser toutes les optimisations
    initialize(): void {
        this.injectCriticalCSS();
        
        // Charger le reste après le DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.loadNonCriticalCSS();
                this.preloadFonts();
            });
        } else {
            this.loadNonCriticalCSS();
            this.preloadFonts();
        }
    }

    // Obtenir les métriques de performance
    getPerformanceMetrics() {
        return {
            criticalCSSSize: new Blob([this.criticalCSS]).size,
            nonCriticalCSSSize: new Blob([this.nonCriticalCSS]).size,
            totalCSSSize: new Blob([this.criticalCSS + this.nonCriticalCSS]).size
        };
    }
}

// Auto-initialisation
if (typeof window !== 'undefined') {
    const extractor = new CriticalCSSExtractor();
    extractor.initialize();
}
EOF

    log_success "Critical CSS configuré"
}

# Fonction principale
main() {
    echo "🚀 DÉMARRAGE OPTIMISATION FRONTEND BUNDLE"
    echo "=========================================="
    
    configure_tree_shaking
    configure_code_splitting
    configure_service_worker
    configure_critical_css
    
    echo ""
    echo "✅ FRONTEND BUNDLE ULTRA-OPTIMISÉ"
    echo "================================="
    echo "📊 Optimisations activées:"
    echo "  • Tree shaking agressif avec Terser"
    echo "  • Code splitting micro-granulaire"
    echo "  • Service worker avec cache intelligent"
    echo "  • Critical CSS inline + async loading"
    echo ""
    echo "🎯 Objectif: Bundle size <300KB"
    echo ""
    
    log_success "Frontend Bundle Ultra-Optimisé - SUCCÈS COMPLET! 🚀"
}

# Exécution du script principal
main "$@"
