console.log('\n🚀 DÉMONSTRATION OPTIMISATIONS DATABASE');
console.log('======================================');

class DatabaseOptimizationDemo {
    constructor() {
        this.queryStats = [];
        this.indexUsage = new Map();
        this.connectionPool = { active: 0, max: 20, queue: 0 };
    }

    // Simuler l'exécution d'une requête optimisée
    async executeQuery(sql, useIndex = true) {
        const startTime = Date.now();
        
        // Simuler l'acquisition d'une connexion du pool
        if (this.connectionPool.active < this.connectionPool.max) {
            this.connectionPool.active++;
        } else {
            this.connectionPool.queue++;
            await new Promise(resolve => setTimeout(resolve, 10)); // Attente pool
            this.connectionPool.queue--;
            this.connectionPool.active++;
        }
        
        // Simuler l'exécution de la requête
        let executionTime;
        if (useIndex) {
            executionTime = Math.random() * 50 + 10; // 10-60ms avec index
            this.indexUsage.set(sql, (this.indexUsage.get(sql) || 0) + 1);
        } else {
            executionTime = Math.random() * 200 + 100; // 100-300ms sans index
        }
        
        await new Promise(resolve => setTimeout(resolve, executionTime));
        
        // Libérer la connexion
        this.connectionPool.active--;
        
        const totalTime = Date.now() - startTime;
        this.queryStats.push({
            sql: sql.substring(0, 50) + '...',
            executionTime: totalTime,
            useIndex,
            timestamp: new Date()
        });
        
        const indexStatus = useIndex ? '🟢 INDEX' : '🔴 TABLE SCAN';
        console.log(`📊 Query: ${totalTime.toFixed(0)}ms ${indexStatus} - ${sql.substring(0, 40)}...`);
        
        return { executionTime: totalTime, useIndex };
    }

    getOptimizationStats() {
        const totalQueries = this.queryStats.length;
        const avgTime = this.queryStats.reduce((sum, q) => sum + q.executionTime, 0) / totalQueries;
        const indexedQueries = this.queryStats.filter(q => q.useIndex).length;
        const slowQueries = this.queryStats.filter(q => q.executionTime > 100).length;
        
        return {
            totalQueries,
            avgExecutionTime: avgTime.toFixed(2),
            indexUsageRatio: ((indexedQueries / totalQueries) * 100).toFixed(1),
            slowQueries,
            connectionPoolUtilization: ((this.connectionPool.active / this.connectionPool.max) * 100).toFixed(1)
        };
    }
}

// Démonstration
async function runDatabaseDemo() {
    const db = new DatabaseOptimizationDemo();
    
    console.log('\n📊 Test de 15 requêtes avec optimisations:');
    console.log('==========================================');
    
    // Simuler différents types de requêtes
    const queries = [
        { sql: 'SELECT * FROM users WHERE id = ?', indexed: true },
        { sql: 'SELECT name, email FROM users WHERE email = ?', indexed: true },
        { sql: 'SELECT * FROM products WHERE category_id = ?', indexed: true },
        { sql: 'SELECT COUNT(*) FROM orders WHERE user_id = ?', indexed: true },
        { sql: 'SELECT * FROM logs WHERE created_at > ?', indexed: true },
        { sql: 'SELECT * FROM users WHERE description LIKE ?', indexed: false }, // Pas d'index sur LIKE
        { sql: 'SELECT u.*, p.* FROM users u JOIN profiles p ON u.id = p.user_id', indexed: true },
        { sql: 'SELECT * FROM products ORDER BY price DESC LIMIT 10', indexed: true },
        { sql: 'SELECT AVG(rating) FROM reviews WHERE product_id = ?', indexed: true },
        { sql: 'SELECT * FROM notifications WHERE user_id = ? AND read = 0', indexed: true },
        { sql: 'UPDATE users SET last_login = NOW() WHERE id = ?', indexed: true },
        { sql: 'SELECT * FROM audit_logs WHERE action = ? AND date > ?', indexed: false }, // Requête lente
        { sql: 'SELECT COUNT(*) FROM sessions WHERE expires_at > NOW()', indexed: true },
        { sql: 'SELECT * FROM cache_entries WHERE key = ?', indexed: true },
        { sql: 'DELETE FROM temp_data WHERE created_at < ?', indexed: true }
    ];
    
    // Exécuter les requêtes
    for (const query of queries) {
        await db.executeQuery(query.sql, query.indexed);
        await new Promise(resolve => setTimeout(resolve, 100)); // Pause pour lisibilité
    }
    
    const stats = db.getOptimizationStats();
    
    console.log('\n🏆 RÉSULTATS OPTIMISATIONS DATABASE:');
    console.log('===================================');
    console.log(`📊 Total Queries: ${stats.totalQueries}`);
    console.log(`📊 Temps Moyen: ${stats.avgExecutionTime}ms`);
    console.log(`📊 Utilisation Index: ${stats.indexUsageRatio}%`);
    console.log(`📊 Requêtes Lentes: ${stats.slowQueries}`);
    console.log(`📊 Pool Utilization: ${stats.connectionPoolUtilization}%`);
    
    if (parseFloat(stats.avgExecutionTime) <= 100) {
        console.log('✅ OBJECTIF ATTEINT: Temps moyen ≤100ms');
    } else {
        console.log('❌ OBJECTIF NON ATTEINT: Temps moyen >100ms');
    }
    
    if (parseFloat(stats.indexUsageRatio) >= 90) {
        console.log('✅ OBJECTIF ATTEINT: >90% utilisation index');
    } else {
        console.log('❌ OBJECTIF NON ATTEINT: <90% utilisation index');
    }
}

runDatabaseDemo().catch(console.error);
