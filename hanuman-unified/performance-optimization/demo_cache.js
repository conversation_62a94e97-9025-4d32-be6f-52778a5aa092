console.log('🚀 DÉMONSTRATION CACHE MULTI-NIVEAU');
console.log('===================================');

// Simuler le cache multi-niveau
class CacheDemo {
    constructor() {
        this.l1Cache = new Map(); // Redis simulé
        this.l2Cache = new Map(); // Hazelcast simulé
        this.l3Cache = new Map(); // MongoDB simulé
        this.stats = { l1Hits: 0, l2Hits: 0, l3Hits: 0, misses: 0 };
    }

    async get(key) {
        // L1 Cache (Redis) - 98% hit ratio
        if (this.l1Cache.has(key)) {
            this.stats.l1Hits++;
            console.log(`✅ L1 HIT: ${key} (Redis - 1ms)`);
            return this.l1Cache.get(key);
        }

        // L2 Cache (Hazelcast) - 95% hit ratio
        if (this.l2Cache.has(key)) {
            this.stats.l2Hits++;
            const value = this.l2Cache.get(key);
            this.l1Cache.set(key, value); // Cache warming
            console.log(`✅ L2 HIT: ${key} (Hazelcast - 3ms) + L1 warming`);
            return value;
        }

        // L3 Cache (MongoDB) - 90% hit ratio
        if (this.l3Cache.has(key)) {
            this.stats.l3Hits++;
            const value = this.l3Cache.get(key);
            this.l2Cache.set(key, value); // Cache warming
            this.l1Cache.set(key, value);
            console.log(`✅ L3 HIT: ${key} (MongoDB - 8ms) + L2+L1 warming`);
            return value;
        }

        // Cache miss complet
        this.stats.misses++;
        const value = `data_${key}_${Date.now()}`;
        
        // Stocker dans tous les niveaux
        this.l3Cache.set(key, value);
        this.l2Cache.set(key, value);
        this.l1Cache.set(key, value);
        
        console.log(`❌ CACHE MISS: ${key} - Données récupérées et mises en cache`);
        return value;
    }

    getStats() {
        const total = this.stats.l1Hits + this.stats.l2Hits + this.stats.l3Hits + this.stats.misses;
        const hitRatio = total > 0 ? ((this.stats.l1Hits + this.stats.l2Hits + this.stats.l3Hits) / total) * 100 : 0;
        
        return {
            ...this.stats,
            total,
            hitRatio: hitRatio.toFixed(2)
        };
    }
}

// Démonstration
async function runCacheDemo() {
    const cache = new CacheDemo();
    
    console.log('\n📊 Test de 20 requêtes avec cache multi-niveau:');
    console.log('================================================');
    
    // Pré-remplir quelques données
    await cache.get('user_1');
    await cache.get('user_2');
    await cache.get('product_1');
    
    // Simuler des requêtes réalistes
    const requests = [
        'user_1', 'user_1', 'user_2', 'product_1', 'user_3',
        'user_1', 'product_2', 'user_2', 'user_4', 'product_1',
        'user_1', 'user_5', 'product_3', 'user_2', 'user_1',
        'product_1', 'user_6', 'user_3', 'product_2', 'user_1'
    ];
    
    for (const key of requests) {
        await cache.get(key);
        await new Promise(resolve => setTimeout(resolve, 50)); // Pause pour lisibilité
    }
    
    const stats = cache.getStats();
    
    console.log('\n🏆 RÉSULTATS CACHE MULTI-NIVEAU:');
    console.log('================================');
    console.log(`📊 L1 Hits (Redis): ${stats.l1Hits}`);
    console.log(`📊 L2 Hits (Hazelcast): ${stats.l2Hits}`);
    console.log(`📊 L3 Hits (MongoDB): ${stats.l3Hits}`);
    console.log(`📊 Cache Misses: ${stats.misses}`);
    console.log(`📊 Total Requests: ${stats.total}`);
    console.log(`🎯 Hit Ratio: ${stats.hitRatio}%`);
    
    if (parseFloat(stats.hitRatio) >= 98) {
        console.log('✅ OBJECTIF ATTEINT: >98% cache hit ratio');
    } else {
        console.log('❌ OBJECTIF NON ATTEINT: <98% cache hit ratio');
    }
}

runCacheDemo().catch(console.error);
