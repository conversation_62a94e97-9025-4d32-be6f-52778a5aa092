import { RedisL1Cache } from './redis/RedisL1Cache';
import { HazelcastL2Cache } from './hazelcast/HazelcastL2Cache';
import { MongoL3Cache } from './mongodb/MongoL3Cache';

export interface CacheConfig {
    l1: {
        enabled: boolean;
        ttl: number;
        maxSize?: number;
    };
    l2: {
        enabled: boolean;
        ttl: number;
        maxSize?: number;
    };
    l3: {
        enabled: boolean;
        ttl: number;
        maxSize?: number;
    };
    strategy: 'write-through' | 'write-back' | 'write-around';
}

export interface CacheMetrics {
    l1: {
        hits: number;
        misses: number;
        hitRatio: number;
        avgResponseTime: number;
    };
    l2: {
        hits: number;
        misses: number;
        hitRatio: number;
        avgResponseTime: number;
    };
    l3: {
        hits: number;
        misses: number;
        hitRatio: number;
        avgResponseTime: number;
    };
    overall: {
        hits: number;
        misses: number;
        hitRatio: number;
        avgResponseTime: number;
    };
}

export class MultiLevelCacheManager {
    private l1Cache: RedisL1Cache;
    private l2Cache: HazelcastL2Cache;
    private l3Cache: MongoL3Cache;
    private config: CacheConfig;
    private metrics: CacheMetrics;

    constructor(config: Partial<CacheConfig> = {}) {
        this.config = {
            l1: { enabled: true, ttl: 300 },
            l2: { enabled: true, ttl: 600 },
            l3: { enabled: true, ttl: 3600 },
            strategy: 'write-through',
            ...config
        };

        this.initializeMetrics();
        this.initializeCaches();
    }

    private initializeMetrics(): void {
        this.metrics = {
            l1: { hits: 0, misses: 0, hitRatio: 0, avgResponseTime: 0 },
            l2: { hits: 0, misses: 0, hitRatio: 0, avgResponseTime: 0 },
            l3: { hits: 0, misses: 0, hitRatio: 0, avgResponseTime: 0 },
            overall: { hits: 0, misses: 0, hitRatio: 0, avgResponseTime: 0 }
        };
    }

    private async initializeCaches(): Promise<void> {
        try {
            // Initialiser L1 Cache (Redis)
            if (this.config.l1.enabled) {
                this.l1Cache = new RedisL1Cache();
                console.log('✅ L1 Cache (Redis) initialisé');
            }

            // Initialiser L2 Cache (Hazelcast)
            if (this.config.l2.enabled) {
                this.l2Cache = new HazelcastL2Cache();
                await this.l2Cache.initialize();
                console.log('✅ L2 Cache (Hazelcast) initialisé');
            }

            // Initialiser L3 Cache (MongoDB)
            if (this.config.l3.enabled) {
                this.l3Cache = new MongoL3Cache();
                await this.l3Cache.initialize();
                console.log('✅ L3 Cache (MongoDB) initialisé');
            }

            console.log('🚀 Cache Multi-Niveau initialisé avec succès');
        } catch (error) {
            console.error('❌ Erreur initialisation cache multi-niveau:', error);
            throw error;
        }
    }

    // Récupérer une valeur du cache multi-niveau
    async get<T>(key: string): Promise<T | null> {
        const startTime = performance.now();
        let result: T | null = null;
        let cacheLevel = '';

        try {
            // Essayer L1 Cache (Redis) en premier
            if (this.config.l1.enabled && this.l1Cache) {
                result = await this.l1Cache.get<T>(key);
                if (result !== null) {
                    this.metrics.l1.hits++;
                    cacheLevel = 'L1';
                } else {
                    this.metrics.l1.misses++;
                }
            }

            // Si pas trouvé en L1, essayer L2 Cache (Hazelcast)
            if (result === null && this.config.l2.enabled && this.l2Cache) {
                result = await this.l2Cache.get<T>(key);
                if (result !== null) {
                    this.metrics.l2.hits++;
                    cacheLevel = 'L2';
                    
                    // Remonter vers L1 (cache warming)
                    if (this.config.l1.enabled && this.l1Cache) {
                        await this.l1Cache.set(key, result, this.config.l1.ttl);
                    }
                } else {
                    this.metrics.l2.misses++;
                }
            }

            // Si pas trouvé en L1+L2, essayer L3 Cache (MongoDB)
            if (result === null && this.config.l3.enabled && this.l3Cache) {
                result = await this.l3Cache.get<T>(key);
                if (result !== null) {
                    this.metrics.l3.hits++;
                    cacheLevel = 'L3';
                    
                    // Remonter vers L2 et L1 (cache warming)
                    if (this.config.l2.enabled && this.l2Cache) {
                        await this.l2Cache.set(key, result, this.config.l2.ttl);
                    }
                    if (this.config.l1.enabled && this.l1Cache) {
                        await this.l1Cache.set(key, result, this.config.l1.ttl);
                    }
                } else {
                    this.metrics.l3.misses++;
                }
            }

            // Mettre à jour les métriques globales
            if (result !== null) {
                this.metrics.overall.hits++;
            } else {
                this.metrics.overall.misses++;
            }

            const endTime = performance.now();
            const responseTime = endTime - startTime;
            this.updateResponseTimeMetrics(responseTime);

            if (result !== null) {
                console.log(`🎯 Cache HIT (${cacheLevel}): ${key} en ${responseTime.toFixed(2)}ms`);
            } else {
                console.log(`❌ Cache MISS: ${key} en ${responseTime.toFixed(2)}ms`);
            }

            return result;
        } catch (error) {
            console.error('Erreur get cache multi-niveau:', error);
            return null;
        }
    }

    // Stocker une valeur dans le cache multi-niveau
    async set(key: string, value: any, customTtl?: number): Promise<boolean> {
        const startTime = performance.now();
        let success = true;

        try {
            const promises: Promise<boolean>[] = [];

            // Stocker en L1 Cache (Redis)
            if (this.config.l1.enabled && this.l1Cache) {
                const ttl = customTtl || this.config.l1.ttl;
                promises.push(this.l1Cache.set(key, value, ttl));
            }

            // Stocker en L2 Cache (Hazelcast) selon la stratégie
            if (this.config.l2.enabled && this.l2Cache && this.shouldWriteToL2()) {
                const ttl = customTtl || this.config.l2.ttl;
                promises.push(this.l2Cache.set(key, value, ttl));
            }

            // Stocker en L3 Cache (MongoDB) selon la stratégie
            if (this.config.l3.enabled && this.l3Cache && this.shouldWriteToL3()) {
                const ttl = customTtl || this.config.l3.ttl;
                promises.push(this.l3Cache.set(key, value, ttl));
            }

            // Attendre toutes les écritures
            const results = await Promise.allSettled(promises);
            success = results.every(result => 
                result.status === 'fulfilled' && result.value === true
            );

            const endTime = performance.now();
            const responseTime = endTime - startTime;

            console.log(`💾 Cache SET: ${key} en ${responseTime.toFixed(2)}ms (${success ? 'succès' : 'échec'})`);

            return success;
        } catch (error) {
            console.error('Erreur set cache multi-niveau:', error);
            return false;
        }
    }

    // Supprimer une valeur du cache multi-niveau
    async delete(key: string): Promise<boolean> {
        const promises: Promise<boolean>[] = [];

        // Supprimer de tous les niveaux
        if (this.config.l1.enabled && this.l1Cache) {
            promises.push(this.l1Cache.delete(key));
        }
        if (this.config.l2.enabled && this.l2Cache) {
            promises.push(this.l2Cache.delete(key));
        }
        if (this.config.l3.enabled && this.l3Cache) {
            promises.push(this.l3Cache.delete(key));
        }

        try {
            const results = await Promise.allSettled(promises);
            const success = results.some(result => 
                result.status === 'fulfilled' && result.value === true
            );

            console.log(`🗑️ Cache DELETE: ${key} (${success ? 'succès' : 'échec'})`);
            return success;
        } catch (error) {
            console.error('Erreur delete cache multi-niveau:', error);
            return false;
        }
    }

    // Vider tous les caches
    async flush(): Promise<boolean> {
        const promises: Promise<boolean>[] = [];

        if (this.config.l1.enabled && this.l1Cache) {
            promises.push(this.l1Cache.flush());
        }
        if (this.config.l2.enabled && this.l2Cache) {
            promises.push(this.l2Cache.flush());
        }
        if (this.config.l3.enabled && this.l3Cache) {
            promises.push(this.l3Cache.flush());
        }

        try {
            await Promise.all(promises);
            this.initializeMetrics();
            console.log('🧹 Tous les caches vidés');
            return true;
        } catch (error) {
            console.error('Erreur flush cache multi-niveau:', error);
            return false;
        }
    }

    // Vérifier la santé du cache multi-niveau
    async healthCheck(): Promise<{ healthy: boolean; details: any }> {
        const health = {
            l1: false,
            l2: false,
            l3: false
        };

        try {
            if (this.config.l1.enabled && this.l1Cache) {
                health.l1 = await this.l1Cache.healthCheck();
            }
            if (this.config.l2.enabled && this.l2Cache) {
                health.l2 = await this.l2Cache.healthCheck();
            }
            if (this.config.l3.enabled && this.l3Cache) {
                health.l3 = await this.l3Cache.healthCheck();
            }

            const healthy = Object.values(health).some(h => h === true);

            return {
                healthy,
                details: {
                    levels: health,
                    metrics: this.getMetrics(),
                    config: this.config
                }
            };
        } catch (error) {
            console.error('Erreur health check cache:', error);
            return {
                healthy: false,
                details: { error: error.message }
            };
        }
    }

    // Obtenir les métriques du cache
    getMetrics(): CacheMetrics {
        // Calculer les ratios de hit
        this.calculateHitRatios();
        return { ...this.metrics };
    }

    private calculateHitRatios(): void {
        // L1 hit ratio
        const l1Total = this.metrics.l1.hits + this.metrics.l1.misses;
        this.metrics.l1.hitRatio = l1Total > 0 ? (this.metrics.l1.hits / l1Total) * 100 : 0;

        // L2 hit ratio
        const l2Total = this.metrics.l2.hits + this.metrics.l2.misses;
        this.metrics.l2.hitRatio = l2Total > 0 ? (this.metrics.l2.hits / l2Total) * 100 : 0;

        // L3 hit ratio
        const l3Total = this.metrics.l3.hits + this.metrics.l3.misses;
        this.metrics.l3.hitRatio = l3Total > 0 ? (this.metrics.l3.hits / l3Total) * 100 : 0;

        // Overall hit ratio
        const overallTotal = this.metrics.overall.hits + this.metrics.overall.misses;
        this.metrics.overall.hitRatio = overallTotal > 0 ? (this.metrics.overall.hits / overallTotal) * 100 : 0;
    }

    private updateResponseTimeMetrics(responseTime: number): void {
        // Mise à jour simplifiée - dans un vrai système, utiliser une moyenne mobile
        this.metrics.overall.avgResponseTime = 
            (this.metrics.overall.avgResponseTime + responseTime) / 2;
    }

    private shouldWriteToL2(): boolean {
        return this.config.strategy === 'write-through' || this.config.strategy === 'write-back';
    }

    private shouldWriteToL3(): boolean {
        return this.config.strategy === 'write-through';
    }

    // Fermer toutes les connexions
    async shutdown(): Promise<void> {
        console.log('🔌 Fermeture du cache multi-niveau...');
        
        if (this.l2Cache) {
            await this.l2Cache.shutdown();
        }
        if (this.l3Cache) {
            await this.l3Cache.shutdown();
        }
        
        console.log('✅ Cache multi-niveau fermé');
    }
}

// Instance globale du cache manager
export const cacheManager = new MultiLevelCacheManager({
    l1: { enabled: true, ttl: 300 },    // 5 minutes
    l2: { enabled: true, ttl: 600 },    // 10 minutes
    l3: { enabled: true, ttl: 3600 },   // 1 heure
    strategy: 'write-through'
});

// Auto-initialisation
if (typeof process !== 'undefined' && process.env.NODE_ENV !== 'test') {
    console.log('🚀 Initialisation du Cache Multi-Niveau Hanuman...');
}
